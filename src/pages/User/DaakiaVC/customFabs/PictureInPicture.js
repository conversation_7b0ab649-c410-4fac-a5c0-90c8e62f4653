import { useRef, useState, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { VideoTrack, useTracks, isTrackReference } from '@livekit/components-react';
import { generateAvatar, parseMetadata } from '../utils/helper';

// Responsive Grid PiP Content Component
function SimplePipContent({ localParticipant }) {
  // Get all tracks (local + remote)
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
  ]);

  // Separate local and remote tracks
  const localCameraTrack = allTracks
    .filter(isTrackReference)
    .find((track) =>
      track.participant.isLocal &&
      track.source === Track.Source.Camera
    );

  const remoteTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => !track.participant.isLocal)
    .slice(0, 3); // Max 3 remote participants

  // Check if local video is enabled
  const isVideoEnabled = localCameraTrack?.publication &&
    !localCameraTrack.publication.isMuted &&
    localCameraTrack.publication.isSubscribed &&
    localCameraTrack.publication.kind === "video";

  // Check if local participant is speaking
  const isSpeaking = localParticipant?.isSpeaking || false;

  // Get participant count for dynamic grid
  const participantCount = remoteTracks.length;

  // Dynamic grid class based on participant count
  const getGridClass = (count) => {
    switch(count) {
      case 0: return 'pip-grid-solo';      // Just local
      case 1: return 'pip-grid-two';       // Local + 1 remote
      case 2: return 'pip-grid-three';     // Local + 2 remote
      case 3: return 'pip-grid-four';      // Local + 3 remote
      default: return 'pip-grid-four';     // Max 4 total
    }
  };

  // Get avatar info for local participant
  const avatarName = localParticipant?.name
    ? generateAvatar(localParticipant.name)
    : generateAvatar(localParticipant.identity);
  let avatarColor = '#7C4DFF';
  try {
    const metaColor = parseMetadata(localParticipant?.metadata)?.color;
    if (metaColor) avatarColor = metaColor;
  } catch (e) { /* ignore */ }

  // Helper function to get remote participant avatar info
  const getRemoteParticipantInfo = (participant) => {
    const name = participant?.name
      ? generateAvatar(participant.name)
      : generateAvatar(participant.identity);
    let color = '#7C4DFF';
    try {
      const metaColor = parseMetadata(participant?.metadata)?.color;
      if (metaColor) color = metaColor;
    } catch (e) { /* ignore */ }
    return { name, color };
  };

  return (
    <div className={`pip-grid-container ${getGridClass(participantCount)}`}>
      {/* Main large tile - Always local participant */}
      <div className={`pip-tile pip-tile-main ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
        {isVideoEnabled && localCameraTrack ? (
          // Show video when camera is on
          <div className="pip-video-container">
            <VideoTrack
              trackRef={localCameraTrack}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                borderRadius: '2vmin'
              }}
            />
          </div>
        ) : (
          // Show avatar when camera is off
          <div className="pip-tile-avatar-center">
            <div
              className="pip-tile-avatar"
              style={{ backgroundColor: avatarColor }}
            >
              {avatarName}
            </div>
          </div>
        )}

        {/* Corner accent lights for local speaking */}
        {isSpeaking && (
          <>
            <div style={{
              position: 'absolute',
              bottom: '1vmin',
              left: '1vmin',
              width: '1.5vmin',
              height: '1.5vmin',
              background: '#1e8cfa',
              borderRadius: '50%',
              zIndex: 2,
              animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              animationDelay: '0.6s'
            }} />
            <div style={{
              position: 'absolute',
              bottom: '1vmin',
              right: '1vmin',
              width: '1.5vmin',
              height: '1.5vmin',
              background: '#1e8cfa',
              borderRadius: '50%',
              zIndex: 2,
              animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              animationDelay: '0.9s'
            }} />
          </>
        )}
      </div>

      {/* Dynamic remote participant tiles */}
      {remoteTracks.map((track, index) => {
        const remoteInfo = getRemoteParticipantInfo(track.participant);
        const isRemoteSpeaking = track.participant?.isSpeaking || false;

        // Check if remote video is enabled
        const isRemoteVideoEnabled = track?.publication &&
          !track.publication.isMuted &&
          track.publication.isSubscribed &&
          track.publication.kind === "video";

        return (
          <div
            key={track.participant.identity || `remote-${index}`}
            className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
          >
            {isRemoteVideoEnabled ? (
              // Show remote video
              <div className="pip-video-container">
                <VideoTrack
                  trackRef={track}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    borderRadius: '2vmin'
                  }}
                />
              </div>
            ) : (
              // Show remote avatar
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: remoteInfo.color }}
                >
                  {remoteInfo.name}
                </div>
              </div>
            )}

            {/* Corner accent lights for remote speaking */}
            {isRemoteSpeaking && (
              <>
                <div style={{
                  position: 'absolute',
                  bottom: '0.5vmin',
                  left: '0.5vmin',
                  width: '1vmin',
                  height: '1vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.6s'
                }} />
                <div style={{
                  position: 'absolute',
                  bottom: '0.5vmin',
                  right: '0.5vmin',
                  width: '1vmin',
                  height: '1vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.9s'
                }} />
              </>
            )}
          </div>
        );
      })}
    </div>
  );
}

export function usePictureInPicture({
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  localParticipant
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Simple configuration
  const defaultConfig = useMemo(() => ({
    width: 320,
    height: 240
  }), []);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Simple PiP Content
  const PipContent = useCallback(() => {
    return <SimplePipContent localParticipant={localParticipant} />;
  }, [localParticipant]);

  // Responsive Grid styles
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100vw;
      height: 100vh;
      background: #000;
      padding: 2vmin;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }

    .pip-grid-container {
      width: 100%;
      height: 100%;
      display: grid;
      gap: 1vmin;
      box-sizing: border-box;
    }

    /* Dynamic grid layouts based on participant count */
    .pip-grid-solo {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr;
    }

    .pip-grid-two {
      grid-template-columns: 1fr;
      grid-template-rows: 2fr 1fr;
    }

    .pip-grid-three {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 2fr 1fr;
    }

    .pip-grid-four {
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 2fr 1fr;
    }

    .pip-tile {
      border: none;
      border-radius: 2vmin;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .pip-tile-main {
      grid-column: 1 / -1;
      grid-row: 1;
      background-color: #1e1e1e;
      border-radius: 2.5vmin;
      transition: box-shadow 0.3s ease, border 0.3s ease;
    }

    .pip-tile-speaking {
      box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3) !important;
      animation: pip-speaking-pulse 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
      position: relative;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform, box-shadow;
    }

    .pip-tile-speaking::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 2.5vmin;
      background: radial-gradient(circle at center, rgba(30, 140, 250, 0.2) 0%, transparent 70%);
      animation: pip-speaking-glow 3s ease-in-out infinite;
      z-index: 0;
      transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: opacity, transform;
    }

    .pip-tile-speaking::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 2.5vmin;
      background: radial-gradient(circle at center, rgba(30, 140, 250, 0.1) 0%, transparent 60%);
      animation: pip-speaking-delay 3s ease-in-out infinite;
      z-index: 0;
      opacity: 0;
      transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: opacity, transform;
    }

    @keyframes pip-speaking-pulse {
      0% {
        box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
        transform: scale(1);
        opacity: 1;
      }
      15% {
        box-shadow: 0 0 0 2.5px #1e8cfa, 0 0 16px rgba(30, 140, 250, 0.4);
        transform: scale(1.003);
        opacity: 1;
      }
      30% {
        box-shadow: 0 0 0 3px #1e8cfa, 0 0 20px rgba(30, 140, 250, 0.5);
        transform: scale(1.006);
        opacity: 1;
      }
      45% {
        box-shadow: 0 0 0 2.5px #1e8cfa, 0 0 16px rgba(30, 140, 250, 0.4);
        transform: scale(1.003);
        opacity: 1;
      }
      60% {
        box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
        transform: scale(1);
        opacity: 0.95;
      }
      100% {
        box-shadow: 0 0 0 2px #1e8cfa, 0 0 12px rgba(30, 140, 250, 0.3);
        transform: scale(1);
        opacity: 1;
      }
    }

    @keyframes pip-speaking-glow {
      0% {
        opacity: 0.15;
        transform: scale(0.98);
      }
      30% {
        opacity: 0.4;
        transform: scale(1.02);
      }
      60% {
        opacity: 0.15;
        transform: scale(0.98);
      }
      100% {
        opacity: 0.15;
        transform: scale(0.98);
      }
    }

    @keyframes pip-speaking-delay {
      0% {
        opacity: 0;
        transform: scale(0.95);
      }
      20% {
        opacity: 0;
        transform: scale(0.95);
      }
      40% {
        opacity: 0.3;
        transform: scale(1.03);
      }
      60% {
        opacity: 0;
        transform: scale(0.95);
      }
      100% {
        opacity: 0;
        transform: scale(0.95);
      }
    }

    .pip-tile-small {
      grid-row: 2;
      aspect-ratio: 16 / 9;
      border-radius: 2vmin;
      transition: box-shadow 0.3s ease, border 0.3s ease;
    }

    .pip-tile-1 {
      background-color: #1e1e1e;
      border-radius: 2vmin;
    }

    .pip-tile-2 {
      background-color: #1e1e1e;
      border-radius: 2vmin;
    }

    .pip-tile-3 {
      background-color: #1e1e1e;
      border-radius: 2vmin;
    }

    .pip-tile-center-dot {
      width: 2vmin;
      height: 2vmin;
      background-color: #ff69b4;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .pip-video-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border-radius: 2vmin;
    }

    .pip-video-container video {
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
      aspect-ratio: 16 / 9 !important;
      max-width: 100%;
      max-height: 100%;
      border-radius: 2vmin !important;
    }

    .pip-tile-pip-custom .lk-participant-metadata {
      display: none !important;
    }
    .pip-tile-participant-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
    }
    .pip-tile-overlay {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      pointer-events: none;
      padding: 0.25em 0.4em 0.35em 0.4em;
      box-sizing: border-box;
    }
    .pip-tile-overlay-left {
      display: flex;
      align-items: center;
      gap: 0.35em;
      background: rgba(20,20,20,0.72);
      border-radius: 0.35em;
      padding: 0.12em 0.5em 0.12em 0.3em;
      font-size: 0.85em;
      color: #fff;
      pointer-events: auto;
      min-height: 1.7em;
    }
    .pip-tile-overlay-left svg {
      width: 1.1em;
      height: 1.1em;
      margin-right: 0.1em;
    }
    .pip-tile-overlay-right {
      display: flex;
      align-items: center;
      background: rgba(20,20,20,0.72);
      border-radius: 0.35em;
      padding: 0.12em 0.4em;
      font-size: 0.85em;
      color: #fff;
      pointer-events: auto;
      min-height: 1.7em;
    }
    .pip-tile-overlay-right svg {
      width: 1.1em;
      height: 1.1em;
    }
    .pip-tile-avatar-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      pointer-events: none;
    }
    .pip-tile-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      font-weight: 600;
      font-size: 8vmin;
      background: #7C4DFF;
      box-shadow: 0 2px 12px rgba(0,0,0,0.12);
      user-select: none;
      pointer-events: auto;
      transition: width 0.2s, height 0.2s, font-size 0.2s;
      aspect-ratio: 1 / 1;
      width: 25vmin;
      height: 25vmin;
    }

    .pip-tile-avatar-small {
      font-size: 4vmin !important;
      width: 12vmin !important;
      height: 12vmin !important;
    }

    /* Responsive media queries for different PiP sizes */
    @media (max-width: 400px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 4vmin;
        padding: 0.8vmin 1.5vmin;
        border-radius: 1.2vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 5vmin;
        height: 5vmin;
      }

      .pip-tile-avatar {
        font-size: 6vmin;
        width: 20vmin;
        height: 20vmin;
      }
    }

    @media (max-width: 300px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3.5vmin;
        padding: 0.6vmin 1.2vmin;
        border-radius: 1vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 4.5vmin;
        height: 4.5vmin;
      }

      .pip-tile-avatar {
        font-size: 5.5vmin;
        width: 18vmin;
        height: 18vmin;
      }
    }

    @media (max-width: 200px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3vmin;
        padding: 0.5vmin 1vmin;
        border-radius: 0.8vmin;
        gap: 0.5vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 4vmin;
        height: 4vmin;
      }

      .pip-tile-avatar {
        font-size: 5vmin;
        width: 15vmin;
        height: 15vmin;
      }
    }

    @media (max-width: 100px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 2.5vmin;
        padding: 0.4vmin 0.8vmin;
        border-radius: 0.6vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 3vmin;
        height: 3vmin;
      }

      .pip-tile-avatar {
        font-size: 4.5vmin;
        width: 12vmin;
        height: 12vmin;
      }
    }

    /* Height-based media queries */
    @media (max-height: 200px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3vmin;
        padding: 0.5vmin 1vmin;
      }

      .pip-tile-avatar {
        font-size: 5vmin;
        width: 15vmin;
        height: 15vmin;
      }
    }

    @media (max-height: 100px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 2.5vmin;
        padding: 0.4vmin 0.8vmin;
      }

      .pip-tile-avatar {
        font-size: 4.5vmin;
        width: 12vmin;
        height: 12vmin;
      }
    }

  `, []);

  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported
  };
}